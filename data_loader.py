import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import json
from datetime import datetime, timedelta
import pandas as pd

class STDataset(Dataset):
    """时空数据集类"""
    def __init__(self, data, len_closeness=3, len_period=1, len_trend=1, 
                 period_interval=1, trend_interval=7, external_data=None):
        """
        初始化时空数据集
        
        Args:
            data: 流量数据 (T, H, W, C)
            len_closeness: 近期依赖长度
            len_period: 周期依赖长度  
            len_trend: 趋势依赖长度
            period_interval: 周期间隔（天）
            trend_interval: 趋势间隔（天）
            external_data: 外部特征数据
        """
        self.data = data
        self.len_closeness = len_closeness
        self.len_period = len_period
        self.len_trend = len_trend
        self.period_interval = period_interval
        self.trend_interval = trend_interval
        self.external_data = external_data
        
        # 计算有效的时间索引
        self.valid_indices = self._get_valid_indices()
        
    def _get_valid_indices(self):
        """获取有效的时间索引"""
        T = self.data.shape[0]
        valid_indices = []
        
        # 计算每天的时间槽数量（假设30分钟间隔，一天48个时间槽）
        slots_per_day = 48
        
        for i in range(T):
            # 检查是否有足够的历史数据
            valid = True
            
            # 近期依赖
            if self.len_closeness > 0:
                if i < self.len_closeness:
                    valid = False
            
            # 周期依赖
            if self.len_period > 0:
                period_start = i - self.period_interval * slots_per_day
                if period_start < self.len_period:
                    valid = False
            
            # 趋势依赖
            if self.len_trend > 0:
                trend_start = i - self.trend_interval * slots_per_day
                if trend_start < self.len_trend:
                    valid = False
            
            if valid:
                valid_indices.append(i)
        
        return valid_indices
    
    def __len__(self):
        return len(self.valid_indices)
    
    def __getitem__(self, idx):
        """获取一个样本"""
        t = self.valid_indices[idx]
        
        # 目标值
        target = self.data[t]  # (H, W, C)
        
        # 近期依赖
        c_data = None
        if self.len_closeness > 0:
            c_indices = list(range(t - self.len_closeness, t))
            c_data = self.data[c_indices]  # (len_closeness, H, W, C)
        
        # 周期依赖
        p_data = None
        if self.len_period > 0:
            slots_per_day = 48
            p_indices = []
            for i in range(self.len_period):
                p_t = t - (self.period_interval * slots_per_day) + i
                p_indices.append(p_t)
            p_data = self.data[p_indices]  # (len_period, H, W, C)
        
        # 趋势依赖
        t_data = None
        if self.len_trend > 0:
            slots_per_day = 48
            t_indices = []
            for i in range(self.len_trend):
                t_t = t - (self.trend_interval * slots_per_day) + i
                t_indices.append(t_t)
            t_data = self.data[t_indices]  # (len_trend, H, W, C)
        
        # 外部特征
        external = None
        if self.external_data is not None:
            external = self.external_data[t]
        
        return (c_data, p_data, t_data, external), target

class STDataProcessor:
    """时空数据处理器"""
    def __init__(self, inflow_file='inflow_data.npy', outflow_file='outflow_data.npy',
                 metadata_file='metadata.json'):
        """
        初始化数据处理器
        
        Args:
            inflow_file: 流入数据文件
            outflow_file: 流出数据文件
            metadata_file: 元数据文件
        """
        self.inflow_file = inflow_file
        self.outflow_file = outflow_file
        self.metadata_file = metadata_file
        
        self.inflow_data = None
        self.outflow_data = None
        self.metadata = None
        self.scaler = None
        
    def load_data(self):
        """加载数据"""
        print("正在加载流量数据...")
        
        self.inflow_data = np.load(self.inflow_file)
        self.outflow_data = np.load(self.outflow_file)
        
        with open(self.metadata_file, 'r') as f:
            self.metadata = json.load(f)
        
        print(f"流入数据形状: {self.inflow_data.shape}")
        print(f"流出数据形状: {self.outflow_data.shape}")
        
        # 合并流入流出数据 (T, H, W, 2)
        self.flow_data = np.stack([self.inflow_data, self.outflow_data], axis=-1)
        print(f"合并后数据形状: {self.flow_data.shape}")
        
    def normalize_data(self, method='minmax'):
        """数据标准化"""
        print(f"正在进行数据标准化（方法: {method}）...")
        
        T, H, W, C = self.flow_data.shape
        
        if method == 'minmax':
            self.scaler = MinMaxScaler()
        elif method == 'standard':
            self.scaler = StandardScaler()
        else:
            raise ValueError("Unsupported normalization method")
        
        # 重塑数据进行标准化
        data_reshaped = self.flow_data.reshape(-1, C)
        data_normalized = self.scaler.fit_transform(data_reshaped)
        self.flow_data = data_normalized.reshape(T, H, W, C)
        
        print("数据标准化完成")
        
    def create_external_features(self):
        """创建外部特征"""
        print("正在创建外部特征...")
        
        T = self.flow_data.shape[0]
        
        # 解析时间槽
        time_slots = [datetime.strptime(t, '%Y-%m-%d %H:%M:%S') for t in self.metadata['time_slots'][:-1]]
        
        external_features = []
        
        for i, time_slot in enumerate(time_slots):
            features = []
            
            # 时间特征
            hour = time_slot.hour
            day_of_week = time_slot.weekday()
            day_of_month = time_slot.day
            month = time_slot.month
            
            # 独热编码小时（24小时）
            hour_onehot = [0] * 24
            hour_onehot[hour] = 1
            
            # 独热编码星期（7天）
            dow_onehot = [0] * 7
            dow_onehot[day_of_week] = 1
            
            # 是否为工作日
            is_weekday = 1 if day_of_week < 5 else 0
            
            # 是否为周末
            is_weekend = 1 if day_of_week >= 5 else 0
            
            # 时间段特征
            is_morning_rush = 1 if 7 <= hour <= 9 else 0
            is_evening_rush = 1 if 17 <= hour <= 19 else 0
            is_night = 1 if hour >= 22 or hour <= 6 else 0
            
            # 组合特征（简化版本，只取前8个）
            features = [
                hour / 23.0,  # 标准化小时
                day_of_week / 6.0,  # 标准化星期
                is_weekday,
                is_weekend,
                is_morning_rush,
                is_evening_rush,
                is_night,
                month / 11.0  # 标准化月份
            ]
            
            external_features.append(features)
        
        self.external_data = np.array(external_features)
        print(f"外部特征形状: {self.external_data.shape}")
        
    def split_data(self, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15):
        """划分数据集"""
        print("正在划分数据集...")
        
        T = self.flow_data.shape[0]
        
        train_size = int(T * train_ratio)
        val_size = int(T * val_ratio)
        test_size = T - train_size - val_size
        
        # 按时间顺序划分
        train_data = self.flow_data[:train_size]
        val_data = self.flow_data[train_size:train_size + val_size]
        test_data = self.flow_data[train_size + val_size:]
        
        train_external = self.external_data[:train_size] if self.external_data is not None else None
        val_external = self.external_data[train_size:train_size + val_size] if self.external_data is not None else None
        test_external = self.external_data[train_size + val_size:] if self.external_data is not None else None
        
        print(f"训练集大小: {train_data.shape[0]}")
        print(f"验证集大小: {val_data.shape[0]}")
        print(f"测试集大小: {test_data.shape[0]}")
        
        return (train_data, val_data, test_data), (train_external, val_external, test_external)
    
    def create_datasets(self, len_closeness=3, len_period=1, len_trend=1):
        """创建PyTorch数据集"""
        print("正在创建PyTorch数据集...")
        
        # 划分数据
        (train_data, val_data, test_data), (train_external, val_external, test_external) = self.split_data()
        
        # 创建数据集
        train_dataset = STDataset(
            train_data, len_closeness, len_period, len_trend, 
            external_data=train_external
        )
        
        val_dataset = STDataset(
            val_data, len_closeness, len_period, len_trend,
            external_data=val_external
        )
        
        test_dataset = STDataset(
            test_data, len_closeness, len_period, len_trend,
            external_data=test_external
        )
        
        print(f"训练数据集样本数: {len(train_dataset)}")
        print(f"验证数据集样本数: {len(val_dataset)}")
        print(f"测试数据集样本数: {len(test_dataset)}")
        
        return train_dataset, val_dataset, test_dataset
    
    def create_data_loaders(self, train_dataset, val_dataset, test_dataset, 
                           batch_size=32, num_workers=0):
        """创建数据加载器"""
        print("正在创建数据加载器...")
        
        def collate_fn(batch):
            """自定义批处理函数"""
            c_batch, p_batch, t_batch, external_batch = [], [], [], []
            target_batch = []
            
            for (c, p, t, external), target in batch:
                if c is not None:
                    c_batch.append(torch.FloatTensor(c))
                if p is not None:
                    p_batch.append(torch.FloatTensor(p))
                if t is not None:
                    t_batch.append(torch.FloatTensor(t))
                if external is not None:
                    external_batch.append(torch.FloatTensor(external))
                
                target_batch.append(torch.FloatTensor(target))
            
            # 堆叠批次数据
            c_tensor = torch.stack(c_batch) if c_batch else None
            p_tensor = torch.stack(p_batch) if p_batch else None
            t_tensor = torch.stack(t_batch) if t_batch else None
            external_tensor = torch.stack(external_batch) if external_batch else None
            target_tensor = torch.stack(target_batch)
            
            # 调整维度顺序: (B, T, H, W, C) -> (B, T, C, H, W)
            if c_tensor is not None:
                c_tensor = c_tensor.permute(0, 1, 4, 2, 3)
            if p_tensor is not None:
                p_tensor = p_tensor.permute(0, 1, 4, 2, 3)
            if t_tensor is not None:
                t_tensor = t_tensor.permute(0, 1, 4, 2, 3)
            
            # 目标张量: (B, H, W, C) -> (B, C, H, W)
            target_tensor = target_tensor.permute(0, 3, 1, 2)
            
            return (c_tensor, p_tensor, t_tensor, external_tensor), target_tensor
        
        train_loader = DataLoader(
            train_dataset, batch_size=batch_size, shuffle=True,
            num_workers=num_workers, collate_fn=collate_fn
        )
        
        val_loader = DataLoader(
            val_dataset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, collate_fn=collate_fn
        )
        
        test_loader = DataLoader(
            test_dataset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, collate_fn=collate_fn
        )
        
        print("数据加载器创建完成")
        
        return train_loader, val_loader, test_loader
    
    def process_all(self, len_closeness=3, len_period=1, len_trend=1, batch_size=32):
        """执行完整的数据处理流程"""
        self.load_data()
        self.normalize_data()
        self.create_external_features()
        
        train_dataset, val_dataset, test_dataset = self.create_datasets(
            len_closeness, len_period, len_trend
        )
        
        train_loader, val_loader, test_loader = self.create_data_loaders(
            train_dataset, val_dataset, test_dataset, batch_size
        )
        
        return train_loader, val_loader, test_loader, self.scaler

if __name__ == "__main__":
    # 测试数据处理器
    processor = STDataProcessor()
    train_loader, val_loader, test_loader, scaler = processor.process_all()
    
    print("\n数据处理完成！")
    print(f"训练批次数: {len(train_loader)}")
    print(f"验证批次数: {len(val_loader)}")
    print(f"测试批次数: {len(test_loader)}")
    
    # 测试一个批次
    for inputs, targets in train_loader:
        c, p, t, external = inputs
        print(f"\n批次数据形状:")
        if c is not None:
            print(f"近期依赖: {c.shape}")
        if p is not None:
            print(f"周期依赖: {p.shape}")
        if t is not None:
            print(f"趋势依赖: {t.shape}")
        if external is not None:
            print(f"外部特征: {external.shape}")
        print(f"目标: {targets.shape}")
        break
