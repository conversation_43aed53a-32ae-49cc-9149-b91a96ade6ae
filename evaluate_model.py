import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import pandas as pd
import os
from datetime import datetime
import json

from st_resnet_model import create_st_resnet_model, STResNetTrainer
from data_loader import STDataProcessor

class ModelEvaluator:
    """模型评估器"""
    def __init__(self, model_path='checkpoints/best_model.pth'):
        self.model_path = model_path
        self.model = None
        self.trainer = None
        self.scaler = None
        self.config = None
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
    def load_model(self):
        """加载训练好的模型"""
        print("正在加载模型...")
        
        checkpoint = torch.load(self.model_path, map_location=self.device)
        self.config = checkpoint['config']
        self.scaler = checkpoint['scaler']
        
        # 重建模型
        self.model = create_st_resnet_model(
            grid_size=self.config['grid_size'],
            nb_flow=self.config['nb_flow'],
            len_closeness=self.config['len_closeness'],
            len_period=self.config['len_period'],
            len_trend=self.config['len_trend'],
            external_dim=self.config['external_dim']
        )
        
        # 加载权重
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.trainer = STResNetTrainer(self.model, self.device)
        
        print(f"模型加载完成，验证损失: {checkpoint['val_loss']:.6f}")
        
    def evaluate_on_test_set(self, test_loader):
        """在测试集上评估模型"""
        print("正在在测试集上评估模型...")
        
        self.model.eval()
        predictions = []
        targets = []
        
        with torch.no_grad():
            for inputs, target in test_loader:
                c, p, t, external = inputs
                
                # 移动到设备
                if c is not None:
                    c = c.to(self.device)
                if p is not None:
                    p = p.to(self.device)
                if t is not None:
                    t = t.to(self.device)
                if external is not None:
                    external = external.to(self.device)
                target = target.to(self.device)
                
                # 预测
                output = self.model([c, p, t, external])
                
                predictions.append(output.cpu().numpy())
                targets.append(target.cpu().numpy())
        
        # 合并所有批次
        predictions = np.concatenate(predictions, axis=0)
        targets = np.concatenate(targets, axis=0)
        
        return predictions, targets
    
    def denormalize_data(self, data):
        """反标准化数据"""
        # data shape: (N, C, H, W)
        N, C, H, W = data.shape
        data_reshaped = data.transpose(0, 2, 3, 1).reshape(-1, C)  # (N*H*W, C)
        data_denorm = self.scaler.inverse_transform(data_reshaped)
        return data_denorm.reshape(N, H, W, C).transpose(0, 3, 1, 2)  # (N, C, H, W)
    
    def calculate_metrics(self, predictions, targets):
        """计算评估指标"""
        print("正在计算评估指标...")
        
        # 反标准化
        pred_denorm = self.denormalize_data(predictions)
        target_denorm = self.denormalize_data(targets)
        
        # 展平数据
        pred_flat = pred_denorm.flatten()
        target_flat = target_denorm.flatten()
        
        # 计算指标
        mse = mean_squared_error(target_flat, pred_flat)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(target_flat, pred_flat)
        r2 = r2_score(target_flat, pred_flat)
        
        # 计算MAPE（平均绝对百分比误差）
        mask = target_flat != 0
        mape = np.mean(np.abs((target_flat[mask] - pred_flat[mask]) / target_flat[mask])) * 100
        
        metrics = {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'R2': r2,
            'MAPE': mape
        }
        
        print("=== 模型评估结果 ===")
        for metric, value in metrics.items():
            print(f"{metric}: {value:.6f}")
        
        return metrics, pred_denorm, target_denorm
    
    def visualize_predictions(self, predictions, targets, save_dir='results'):
        """可视化预测结果"""
        print("正在生成可视化结果...")
        
        os.makedirs(save_dir, exist_ok=True)
        
        # 选择几个时间步进行可视化
        num_samples = min(6, predictions.shape[0])
        sample_indices = np.linspace(0, predictions.shape[0]-1, num_samples, dtype=int)
        
        fig, axes = plt.subplots(num_samples, 4, figsize=(20, 5*num_samples))
        if num_samples == 1:
            axes = axes.reshape(1, -1)
        
        for i, idx in enumerate(sample_indices):
            # 流入量预测 vs 真实
            im1 = axes[i, 0].imshow(predictions[idx, 0], cmap='hot', vmin=0)
            axes[i, 0].set_title(f'预测流入量 (t={idx})')
            plt.colorbar(im1, ax=axes[i, 0])
            
            im2 = axes[i, 1].imshow(targets[idx, 0], cmap='hot', vmin=0)
            axes[i, 1].set_title(f'真实流入量 (t={idx})')
            plt.colorbar(im2, ax=axes[i, 1])
            
            # 流出量预测 vs 真实
            im3 = axes[i, 2].imshow(predictions[idx, 1], cmap='hot', vmin=0)
            axes[i, 2].set_title(f'预测流出量 (t={idx})')
            plt.colorbar(im3, ax=axes[i, 2])
            
            im4 = axes[i, 3].imshow(targets[idx, 1], cmap='hot', vmin=0)
            axes[i, 3].set_title(f'真实流出量 (t={idx})')
            plt.colorbar(im4, ax=axes[i, 3])
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'prediction_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        # 散点图：预测 vs 真实
        plt.figure(figsize=(12, 5))
        
        # 流入量散点图
        plt.subplot(1, 2, 1)
        pred_inflow = predictions[:, 0].flatten()
        target_inflow = targets[:, 0].flatten()
        plt.scatter(target_inflow, pred_inflow, alpha=0.5, s=1)
        plt.plot([target_inflow.min(), target_inflow.max()], 
                [target_inflow.min(), target_inflow.max()], 'r--', lw=2)
        plt.xlabel('真实流入量')
        plt.ylabel('预测流入量')
        plt.title('流入量预测散点图')
        plt.grid(True)
        
        # 流出量散点图
        plt.subplot(1, 2, 2)
        pred_outflow = predictions[:, 1].flatten()
        target_outflow = targets[:, 1].flatten()
        plt.scatter(target_outflow, pred_outflow, alpha=0.5, s=1)
        plt.plot([target_outflow.min(), target_outflow.max()], 
                [target_outflow.min(), target_outflow.max()], 'r--', lw=2)
        plt.xlabel('真实流出量')
        plt.ylabel('预测流出量')
        plt.title('流出量预测散点图')
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'scatter_plots.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
        
        # 时间序列图
        plt.figure(figsize=(15, 8))
        
        # 计算总流量
        total_pred_inflow = np.sum(predictions[:, 0], axis=(1, 2))
        total_target_inflow = np.sum(targets[:, 0], axis=(1, 2))
        total_pred_outflow = np.sum(predictions[:, 1], axis=(1, 2))
        total_target_outflow = np.sum(targets[:, 1], axis=(1, 2))
        
        time_steps = range(len(total_pred_inflow))
        
        plt.subplot(2, 1, 1)
        plt.plot(time_steps, total_target_inflow, label='真实流入量', alpha=0.7)
        plt.plot(time_steps, total_pred_inflow, label='预测流入量', alpha=0.7)
        plt.xlabel('时间步')
        plt.ylabel('总流入量')
        plt.title('总流入量时间序列')
        plt.legend()
        plt.grid(True)
        
        plt.subplot(2, 1, 2)
        plt.plot(time_steps, total_target_outflow, label='真实流出量', alpha=0.7)
        plt.plot(time_steps, total_pred_outflow, label='预测流出量', alpha=0.7)
        plt.xlabel('时间步')
        plt.ylabel('总流出量')
        plt.title('总流出量时间序列')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'time_series.png'), 
                   dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_results(self, metrics, predictions, targets, save_dir='results'):
        """保存评估结果"""
        print("正在保存评估结果...")
        
        os.makedirs(save_dir, exist_ok=True)
        
        # 保存指标
        with open(os.path.join(save_dir, 'evaluation_metrics.json'), 'w') as f:
            json.dump(metrics, f, indent=2)
        
        # 保存预测结果
        np.save(os.path.join(save_dir, 'predictions.npy'), predictions)
        np.save(os.path.join(save_dir, 'targets.npy'), targets)
        
        print(f"结果保存在: {save_dir}")

def evaluate_st_resnet(model_path='checkpoints/best_model.pth'):
    """评估ST-ResNet模型"""
    print("开始评估ST-ResNet模型...")
    
    # 创建评估器
    evaluator = ModelEvaluator(model_path)
    evaluator.load_model()
    
    # 准备测试数据
    print("\n=== 准备测试数据 ===")
    data_processor = STDataProcessor()
    train_loader, val_loader, test_loader, _ = data_processor.process_all(
        len_closeness=evaluator.config['len_closeness'],
        len_period=evaluator.config['len_period'],
        len_trend=evaluator.config['len_trend'],
        batch_size=evaluator.config['batch_size']
    )
    
    # 评估模型
    print("\n=== 模型评估 ===")
    predictions, targets = evaluator.evaluate_on_test_set(test_loader)
    
    # 计算指标
    metrics, pred_denorm, target_denorm = evaluator.calculate_metrics(predictions, targets)
    
    # 可视化结果
    print("\n=== 生成可视化 ===")
    evaluator.visualize_predictions(pred_denorm, target_denorm)
    
    # 保存结果
    evaluator.save_results(metrics, pred_denorm, target_denorm)
    
    print("\n模型评估完成！")
    return metrics, pred_denorm, target_denorm

if __name__ == "__main__":
    # 评估模型
    metrics, predictions, targets = evaluate_st_resnet()
    
    print("\n评估脚本执行完成！")
