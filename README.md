# 城市出租车出行流量预测 - ST-ResNet深度学习模型

基于深度学习残差网络ST-ResNet的城市出租车出行流量预测系统，使用OD（Origin-Destination）数据进行时空流量建模和预测。

## 项目概述

本项目实现了一个完整的城市出租车出行流量预测系统，包括：

- **数据预处理**: 将原始OD数据转换为时空网格流量矩阵
- **模型架构**: 实现ST-ResNet深度残差网络，融合近期、周期和趋势依赖
- **模型训练**: 支持GPU加速训练，包含早停、学习率调度等优化策略
- **模型评估**: 多维度评估指标和可视化分析

## 文件结构

```
├── OD_ALL.csv                 # 原始OD数据文件
├── main.py                    # 主执行脚本
├── data_preprocessing.py      # 数据预处理模块
├── st_resnet_model.py        # ST-ResNet模型定义
├── data_loader.py            # 数据加载和处理
├── train_model.py            # 模型训练脚本
├── evaluate_model.py         # 模型评估脚本
├── README.md                 # 项目说明文档
├── checkpoints/              # 模型检查点目录
├── logs/                     # 训练日志目录
└── results/                  # 评估结果目录
```

## 数据格式

输入数据`OD_ALL.csv`应包含以下字段：

| 字段名 | 描述 | 示例 |
|--------|------|------|
| pid | 记录ID | 0, 1, 2, ... |
| ID | 行程ID | 201608010000000 |
| uid | 用户ID | 00031346d553c683... |
| otime | 上车时间戳 | 1470010247 |
| olat | 上车纬度 | 39.88319 |
| olon | 上车经度 | 116.45162 |
| ogbcode | 上车区域编码 | 110105017 |
| dtime | 下车时间戳 | 1470024596 |
| dlat | 下车纬度 | 39.92252 |
| dlon | 下车经度 | 116.30162 |
| dgbcode | 下车区域编码 | 110108004 |

## 环境要求

### Python版本
- Python 3.7+

### 依赖包
```bash
pip install torch torchvision numpy pandas matplotlib seaborn scikit-learn
```

### 硬件要求
- **内存**: 建议8GB以上
- **GPU**: 可选，支持CUDA加速训练
- **存储**: 至少2GB可用空间

## 快速开始

### 1. 准备数据
确保`OD_ALL.csv`文件在项目根目录下。

### 2. 执行完整流程
```bash
python main.py
```

### 3. 分步执行
```bash
# 仅数据预处理
python main.py --mode preprocess

# 仅模型训练
python main.py --mode train

# 仅模型评估
python main.py --mode evaluate
```

## 模型参数配置

可以在`train_model.py`中的`TrainingConfig`类中修改以下参数：

### 模型参数
- `grid_size`: 空间网格大小 (默认: 32x32)
- `nb_flow`: 流量类型数量 (默认: 2, 流入+流出)
- `len_closeness`: 近期依赖长度 (默认: 3)
- `len_period`: 周期依赖长度 (默认: 1)
- `len_trend`: 趋势依赖长度 (默认: 1)
- `external_dim`: 外部特征维度 (默认: 8)

### 训练参数
- `batch_size`: 批次大小 (默认: 32)
- `num_epochs`: 训练轮数 (默认: 100)
- `learning_rate`: 学习率 (默认: 0.001)
- `weight_decay`: 权重衰减 (默认: 1e-4)

## 输出文件说明

### 数据预处理输出
- `inflow_data.npy`: 流入量时空矩阵 (T, H, W)
- `outflow_data.npy`: 流出量时空矩阵 (T, H, W)
- `netflow_data.npy`: 净流量时空矩阵 (T, H, W)
- `metadata.json`: 元数据信息
- `data_analysis.png`: 数据分析可视化

### 模型训练输出
- `checkpoints/best_model.pth`: 验证集上最佳模型
- `checkpoints/final_model.pth`: 最终训练模型
- `logs/training_log_*.json`: 详细训练日志
- `logs/training_curves.png`: 训练损失曲线

### 模型评估输出
- `results/evaluation_metrics.json`: 评估指标 (MSE, RMSE, MAE, R², MAPE)
- `results/predictions.npy`: 模型预测结果
- `results/targets.npy`: 真实标签
- `results/prediction_comparison.png`: 预测vs真实对比图
- `results/scatter_plots.png`: 预测散点图
- `results/time_series.png`: 时间序列对比图

## 模型架构

ST-ResNet (Spatio-Temporal Residual Network) 包含以下组件：

1. **多分支输入**:
   - 近期依赖 (Closeness): 最近几个时间步的流量
   - 周期依赖 (Period): 相同时间的历史流量
   - 趋势依赖 (Trend): 长期趋势流量

2. **残差单元**: 深度卷积残差块，提取空间特征

3. **融合层**: 将多分支特征和外部特征融合

4. **外部特征**: 时间特征（小时、星期、节假日等）

## 评估指标

- **MSE**: 均方误差
- **RMSE**: 均方根误差  
- **MAE**: 平均绝对误差
- **R²**: 决定系数
- **MAPE**: 平均绝对百分比误差

## 常见问题

### Q: 内存不足怎么办？
A: 可以减小`batch_size`或`grid_size`参数。

### Q: 训练时间太长怎么办？
A: 可以减少`num_epochs`或使用GPU加速。

### Q: 如何调整模型性能？
A: 可以尝试调整网络深度、学习率、或增加外部特征。

### Q: 数据格式不匹配怎么办？
A: 请确保CSV文件包含所有必需字段，并检查数据类型。

## 技术支持

如有问题，请检查：
1. 数据文件格式是否正确
2. 依赖包是否完整安装
3. 系统内存是否充足
4. 错误日志信息

## 许可证

本项目仅供学习和研究使用。

## 更新日志

- v1.0.0: 初始版本，实现完整的ST-ResNet流量预测系统
