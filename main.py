#!/usr/bin/env python3
"""
城市出租车出行流量预测 - ST-ResNet深度学习模型
主执行脚本

使用方法:
python main.py --mode [preprocess|train|evaluate|all]

模式说明:
- preprocess: 仅执行数据预处理
- train: 仅执行模型训练
- evaluate: 仅执行模型评估
- all: 执行完整流程（默认）
"""

import argparse
import os
import sys
import time
from datetime import datetime

def run_data_preprocessing():
    """执行数据预处理"""
    print("=" * 60)
    print("步骤 1: 数据预处理")
    print("=" * 60)
    
    try:
        from data_preprocessing import TaxiDataProcessor
        
        # 检查数据文件是否存在
        if not os.path.exists('OD_ALL.csv'):
            print("错误: 找不到 OD_ALL.csv 文件")
            print("请确保数据文件在当前目录下")
            return False
        
        # 创建数据处理器并执行处理
        processor = TaxiDataProcessor('OD_ALL.csv', grid_size=32, time_interval=30)
        flow_data = processor.process_all()
        
        print("数据预处理完成！")
        return True
        
    except Exception as e:
        print(f"数据预处理失败: {str(e)}")
        return False

def run_model_training():
    """执行模型训练"""
    print("=" * 60)
    print("步骤 2: 模型训练")
    print("=" * 60)
    
    try:
        from train_model import train_st_resnet
        
        # 检查预处理数据是否存在
        required_files = ['inflow_data.npy', 'outflow_data.npy', 'metadata.json']
        for file in required_files:
            if not os.path.exists(file):
                print(f"错误: 找不到预处理文件 {file}")
                print("请先运行数据预处理步骤")
                return False
        
        # 训练模型
        model, trainer, test_loader, scaler = train_st_resnet()
        
        print("模型训练完成！")
        return True
        
    except Exception as e:
        print(f"模型训练失败: {str(e)}")
        return False

def run_model_evaluation():
    """执行模型评估"""
    print("=" * 60)
    print("步骤 3: 模型评估")
    print("=" * 60)
    
    try:
        from evaluate_model import evaluate_st_resnet
        
        # 检查模型文件是否存在
        model_path = 'checkpoints/best_model.pth'
        if not os.path.exists(model_path):
            print(f"错误: 找不到训练好的模型文件 {model_path}")
            print("请先运行模型训练步骤")
            return False
        
        # 评估模型
        metrics, predictions, targets = evaluate_st_resnet(model_path)
        
        print("模型评估完成！")
        return True
        
    except Exception as e:
        print(f"模型评估失败: {str(e)}")
        return False

def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")
    
    required_packages = [
        'torch', 'numpy', 'pandas', 'matplotlib', 'seaborn', 
        'scikit-learn', 'json'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("所有依赖包已安装")
    return True

def print_system_info():
    """打印系统信息"""
    print("=" * 60)
    print("系统信息")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    except ImportError:
        print("PyTorch未安装")
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='城市出租车出行流量预测 - ST-ResNet深度学习模型',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                    # 执行完整流程
  python main.py --mode preprocess  # 仅数据预处理
  python main.py --mode train       # 仅模型训练
  python main.py --mode evaluate    # 仅模型评估
        """
    )
    
    parser.add_argument(
        '--mode', 
        choices=['preprocess', 'train', 'evaluate', 'all'],
        default='all',
        help='执行模式 (默认: all)'
    )
    
    args = parser.parse_args()
    
    # 打印标题
    print("=" * 60)
    print("城市出租车出行流量预测")
    print("ST-ResNet深度学习模型")
    print("=" * 60)
    
    # 打印系统信息
    print_system_info()
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 记录开始时间
    start_time = time.time()
    
    # 根据模式执行相应步骤
    success = True
    
    if args.mode in ['preprocess', 'all']:
        success &= run_data_preprocessing()
        if not success and args.mode == 'all':
            print("数据预处理失败，停止执行")
            return 1
    
    if args.mode in ['train', 'all']:
        success &= run_model_training()
        if not success and args.mode == 'all':
            print("模型训练失败，停止执行")
            return 1
    
    if args.mode in ['evaluate', 'all']:
        success &= run_model_evaluation()
    
    # 计算总执行时间
    total_time = time.time() - start_time
    hours = int(total_time // 3600)
    minutes = int((total_time % 3600) // 60)
    seconds = int(total_time % 60)
    
    print("=" * 60)
    if success:
        print("所有步骤执行成功！")
    else:
        print("部分步骤执行失败，请检查错误信息")
    
    print(f"总执行时间: {hours:02d}:{minutes:02d}:{seconds:02d}")
    print("=" * 60)
    
    # 打印输出文件信息
    if success:
        print("\n生成的文件:")
        
        if args.mode in ['preprocess', 'all']:
            print("数据预处理输出:")
            print("  - inflow_data.npy: 流入量数据")
            print("  - outflow_data.npy: 流出量数据")
            print("  - netflow_data.npy: 净流量数据")
            print("  - metadata.json: 元数据信息")
            print("  - data_analysis.png: 数据分析图表")
        
        if args.mode in ['train', 'all']:
            print("模型训练输出:")
            print("  - checkpoints/best_model.pth: 最佳模型")
            print("  - checkpoints/final_model.pth: 最终模型")
            print("  - logs/training_log_*.json: 训练日志")
            print("  - logs/training_curves.png: 训练曲线")
        
        if args.mode in ['evaluate', 'all']:
            print("模型评估输出:")
            print("  - results/evaluation_metrics.json: 评估指标")
            print("  - results/predictions.npy: 预测结果")
            print("  - results/targets.npy: 真实值")
            print("  - results/prediction_comparison.png: 预测对比图")
            print("  - results/scatter_plots.png: 散点图")
            print("  - results/time_series.png: 时间序列图")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
