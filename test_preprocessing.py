#!/usr/bin/env python3
"""
测试数据预处理脚本 - 使用小样本数据
"""

import pandas as pd
import numpy as np
import geopandas as gpd
from shapely.geometry import Point
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def test_data_loading():
    """测试数据加载"""
    print("=== 测试数据加载 ===")
    
    try:
        # 加载少量数据进行测试
        data = pd.read_csv('OD_ALL.csv', nrows=10000)  # 只读取前10000行
        print(f"成功加载{len(data)}条测试数据")
        print(f"数据列: {list(data.columns)}")
        print(f"数据样本:\n{data.head()}")
        return data
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def test_shapefile_loading():
    """测试shp文件加载"""
    print("\n=== 测试shp文件加载 ===")
    
    try:
        shp_file = "SAU/SAU/Grid/BeijingGrid_1km_3857.shp"
        grid_gdf = gpd.read_file(shp_file)
        print(f"成功加载网格文件，共{len(grid_gdf)}个网格")
        print(f"坐标系: {grid_gdf.crs}")
        print(f"网格列: {list(grid_gdf.columns)}")
        print(f"网格边界: {grid_gdf.total_bounds}")
        return grid_gdf
    except Exception as e:
        print(f"shp文件加载失败: {e}")
        return None

def test_spatial_join():
    """测试空间连接"""
    print("\n=== 测试空间连接 ===")
    
    # 加载测试数据
    data = test_data_loading()
    grid_gdf = test_shapefile_loading()
    
    if data is None or grid_gdf is None:
        print("无法进行空间连接测试")
        return False
    
    try:
        # 数据清洗
        data = data.dropna()
        data = data[
            (data['olon'] >= 115.7) & (data['olon'] <= 117.4) &
            (data['olat'] >= 39.4) & (data['olat'] <= 41.6)
        ]
        
        print(f"清洗后数据量: {len(data)}")
        
        # 转换坐标系
        if grid_gdf.crs != 'EPSG:4326':
            grid_wgs84 = grid_gdf.to_crs('EPSG:4326')
        else:
            grid_wgs84 = grid_gdf.copy()
        
        # 创建点几何
        pickup_points = [Point(lon, lat) for lon, lat in zip(data['olon'][:100], data['olat'][:100])]  # 只测试前100个点
        pickup_gdf = gpd.GeoDataFrame(geometry=pickup_points, crs='EPSG:4326')
        
        # 添加网格ID列
        if 'ID' in grid_wgs84.columns:
            grid_wgs84['grid_id'] = grid_wgs84['ID']
        elif 'id' in grid_wgs84.columns:
            grid_wgs84['grid_id'] = grid_wgs84['id']
        else:
            grid_wgs84['grid_id'] = range(len(grid_wgs84))
        
        # 空间连接
        joined = gpd.sjoin(pickup_gdf, grid_wgs84, how='left', predicate='within')
        
        matched_count = joined['grid_id'].notna().sum()
        print(f"成功匹配{matched_count}/{len(pickup_points)}个点到网格")
        
        if matched_count > 0:
            print("空间连接测试成功！")
            return True
        else:
            print("空间连接测试失败：没有点匹配到网格")
            return False
            
    except Exception as e:
        print(f"空间连接测试失败: {e}")
        return False

def test_time_processing():
    """测试时间处理"""
    print("\n=== 测试时间处理 ===")
    
    try:
        # 创建测试时间数据
        test_timestamps = [1470009600, 1470013200, 1470016800]  # 2016年8月1日的几个时间点
        test_dt = pd.to_datetime(test_timestamps, unit='s')
        
        print(f"测试时间戳: {test_timestamps}")
        print(f"转换后时间: {test_dt}")
        
        # 创建时间网格
        start_time = pd.Timestamp('2016-08-01 00:00:00')
        end_time = pd.Timestamp('2016-08-01 23:59:59')
        time_slots = pd.date_range(start=start_time, end=end_time, freq='30min')
        
        print(f"时间槽数量: {len(time_slots)}")
        print(f"时间范围: {time_slots[0]} 到 {time_slots[-1]}")
        
        # 分配时间槽
        time_slot_indices = pd.cut(test_dt, bins=time_slots, labels=False, include_lowest=True)
        print(f"时间槽索引: {time_slot_indices}")
        
        print("时间处理测试成功！")
        return True
        
    except Exception as e:
        print(f"时间处理测试失败: {e}")
        return False

def test_flow_matrix():
    """测试流量矩阵生成"""
    print("\n=== 测试流量矩阵生成 ===")
    
    try:
        # 创建小规模测试矩阵
        num_time_slots = 48  # 一天48个时间槽
        grid_size = 10       # 10x10网格
        
        print(f"创建测试矩阵: ({num_time_slots}, {grid_size}, {grid_size})")
        
        inflow = np.zeros((num_time_slots, grid_size, grid_size), dtype=np.float32)
        outflow = np.zeros((num_time_slots, grid_size, grid_size), dtype=np.float32)
        
        # 添加一些测试数据
        inflow[10, 5, 5] = 10
        outflow[10, 5, 5] = 8
        
        netflow = inflow - outflow
        
        print(f"流入矩阵形状: {inflow.shape}")
        print(f"流出矩阵形状: {outflow.shape}")
        print(f"净流量矩阵形状: {netflow.shape}")
        print(f"测试位置净流量: {netflow[10, 5, 5]}")
        
        print("流量矩阵测试成功！")
        return True
        
    except Exception as e:
        print(f"流量矩阵测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("ST-ResNet数据预处理系统测试")
    print("=" * 60)
    
    tests = [
        ("数据加载", test_data_loading),
        ("shp文件加载", test_shapefile_loading),
        ("空间连接", test_spatial_join),
        ("时间处理", test_time_processing),
        ("流量矩阵", test_flow_matrix)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_name in ["数据加载", "shp文件加载"]:
                # 这些测试返回数据，不是布尔值
                result = test_func()
                success = result is not None
            else:
                success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"{test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:15} : {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统准备就绪。")
    else:
        print("⚠️  部分测试失败，请检查环境配置。")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
