import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class TaxiDataProcessor:
    def __init__(self, csv_file, grid_size=32, time_interval=30):
        """
        初始化出租车数据处理器
        
        Args:
            csv_file: OD数据文件路径
            grid_size: 网格大小 (grid_size x grid_size)
            time_interval: 时间间隔（分钟）
        """
        self.csv_file = csv_file
        self.grid_size = grid_size
        self.time_interval = time_interval
        self.data = None
        self.flow_data = None
        
    def load_data(self):
        """加载OD数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.csv_file)
        print(f"数据加载完成，共{len(self.data)}条记录")
        return self.data
    
    def explore_data(self):
        """数据探索和分析"""
        print("\n=== 数据基本信息 ===")
        print(f"数据形状: {self.data.shape}")
        print(f"列名: {list(self.data.columns)}")
        print(f"数据类型:\n{self.data.dtypes}")
        
        print("\n=== 缺失值统计 ===")
        print(self.data.isnull().sum())
        
        print("\n=== 数据样本 ===")
        print(self.data.head())
        
        # 时间范围分析
        # self.data['otime_dt'] = pd.to_datetime(self.data['otime'], unit='s')
        # self.data['dtime_dt'] = pd.to_datetime(self.data['dtime'], unit='s')
        self.data['dtime'] = pd.to_numeric(self.data['dtime'], errors='coerce')
        self.data['otime'] = pd.to_numeric(self.data['otime'], errors='coerce')
            
            # 使用Pandas的to_datetime函数处理Unix时间戳（单位：秒）
            # 注意：utc=True表示生成的是UTC时间
        self.data['dtime_utc'] = pd.to_datetime(self.data['dtime'], unit='s', utc=True)
        self.data['otime_utc'] = pd.to_datetime(self.data['otime'], unit='s', utc=True)
            
            # 转换为北京时间（UTC+8）
            # 使用tz_convert方法明确转换时区，而不是简单地加8小时
            #.dt表示
        self.data['dtime_bj'] = self.data['dtime_utc'].dt.tz_convert('Asia/Shanghai')
        self.data['otime_bj'] = self.data['otime_utc'].dt.tz_convert('Asia/Shanghai')


        # 将时区-aware的datetime转换为固定格式字符串（不含时区信息）
        self.data['dtime_fmt'] = self.data['dtime_bj'].dt.tz_localize(None)
        self.data['otime_fmt'] = self.data['otime_bj'].dt.tz_localize(None)    
        
        print(f"\n=== 时间范围 ===")
        print(f"上车时间范围: {self.data['otime_fmt'].min()} 到 {self.data['otime_fmt'].max()}")
        print(f"下车时间范围: {self.data['dtime_fmt'].min()} 到 {self.data['dtime_fmt'].max()}")
        
        # 地理范围分析
        print(f"\n=== 地理范围 ===")
        print(f"上车经度范围: {self.data['olon'].min():.6f} 到 {self.data['olon'].max():.6f}")
        print(f"上车纬度范围: {self.data['olat'].min():.6f} 到 {self.data['olat'].max():.6f}")
        print(f"下车经度范围: {self.data['dlon'].min():.6f} 到 {self.data['dlon'].max():.6f}")
        print(f"下车纬度范围: {self.data['dlat'].min():.6f} 到 {self.data['dlat'].max():.6f}")
        
    def clean_data(self):
        """数据清洗"""
        print("\n正在进行数据清洗...")
        original_len = len(self.data)
        
        # 删除缺失值
        self.data = self.data.dropna()
        
        # 删除异常经纬度数据（北京地区范围）
        # 北京大致范围：经度115.7-117.4，纬度39.4-41.6
        self.data = self.data[
            (self.data['olon'] >= 115.7) & (self.data['olon'] <= 117.4) &
            (self.data['olat'] >= 39.4) & (self.data['olat'] <= 41.6) &
            (self.data['dlon'] >= 115.7) & (self.data['dlon'] <= 117.4) &
            (self.data['dlat'] >= 39.4) & (self.data['dlat'] <= 41.6)
        ]
        
        # 删除行程时间异常的数据（行程时间应为正数且合理）
        self.data['trip_duration'] = (self.data['dtime_fmt'] - self.data['otime_fmt']).dt.total_seconds()
        self.data = self.data[
            (self.data['trip_duration'] > 0) & 
            (self.data['trip_duration'] < 3600 * 6)  # 小于6小时
        ]
        
        print(f"数据清洗完成，从{original_len}条记录减少到{len(self.data)}条记录")
        
    def create_spatial_grid(self):
        """创建空间网格"""
        #使用已有的shp文件
        
        
        print(f"\n正在创建{self.grid_size}x{self.grid_size}空间网格...")
        
        # 计算经纬度范围
        min_lon = self.data[['olon', 'dlon']].min().min()
        max_lon = self.data[['olon', 'dlon']].max().max()
        min_lat = self.data[['olat', 'dlat']].min().min()
        max_lat = self.data[['olat', 'dlat']].max().max()
        
        # 创建网格边界
        lon_bins = np.linspace(min_lon, max_lon, self.grid_size + 1)
        lat_bins = np.linspace(min_lat, max_lat, self.grid_size + 1)
        
        # 为上车和下车位置分配网格索引
        self.data['o_grid_x'] = np.digitize(self.data['olon'], lon_bins) - 1
        self.data['o_grid_y'] = np.digitize(self.data['olat'], lat_bins) - 1
        self.data['d_grid_x'] = np.digitize(self.data['dlon'], lon_bins) - 1
        self.data['d_grid_y'] = np.digitize(self.data['dlat'], lat_bins) - 1
        
        # 确保网格索引在有效范围内
        self.data['o_grid_x'] = np.clip(self.data['o_grid_x'], 0, self.grid_size - 1)
        self.data['o_grid_y'] = np.clip(self.data['o_grid_y'], 0, self.grid_size - 1)
        self.data['d_grid_x'] = np.clip(self.data['d_grid_x'], 0, self.grid_size - 1)
        self.data['d_grid_y'] = np.clip(self.data['d_grid_y'], 0, self.grid_size - 1)
        
        self.lon_bins = lon_bins
        self.lat_bins = lat_bins
        
        print("空间网格创建完成")
        
    def create_temporal_grid(self):
        """创建时间网格"""
        print(f"\n正在创建时间网格（间隔{self.time_interval}分钟）...")
        
        # 获取时间范围
        start_time = self.data['otime_fmt'].min().replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = self.data['dtime_fmt'].max().replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 创建时间间隔
        time_slots = pd.date_range(start=start_time, end=end_time, 
                                  freq=f'{self.time_interval}min')
        
        # 为每条记录分配时间槽
        self.data['o_time_slot'] = pd.cut(self.data['otime_fmt'], bins=time_slots, 
                                         labels=False, include_lowest=True)
        self.data['d_time_slot'] = pd.cut(self.data['dtime_fmt'], bins=time_slots, 
                                         labels=False, include_lowest=True)
        
        self.time_slots = time_slots
        self.num_time_slots = len(time_slots) - 1
        
        print(f"时间网格创建完成，共{self.num_time_slots}个时间槽")
        
    def generate_flow_matrices(self):
        """生成流量矩阵"""
        print("\n正在生成流量矩阵...")
        
        # 初始化流入和流出矩阵
        inflow = np.zeros((self.num_time_slots, self.grid_size, self.grid_size))
        outflow = np.zeros((self.num_time_slots, self.grid_size, self.grid_size))
        
        # 统计每个时空网格的流入流出量
        for _, row in self.data.iterrows():
            # 流出（上车）
            if not pd.isna(row['o_time_slot']):
                t_slot = int(row['o_time_slot'])
                if 0 <= t_slot < self.num_time_slots:
                    outflow[t_slot, int(row['o_grid_y']), int(row['o_grid_x'])] += 1
            
            # 流入（下车）
            if not pd.isna(row['d_time_slot']):
                t_slot = int(row['d_time_slot'])
                if 0 <= t_slot < self.num_time_slots:
                    inflow[t_slot, int(row['d_grid_y']), int(row['d_grid_x'])] += 1
        
        # 计算净流量
        netflow = inflow - outflow
        
        self.flow_data = {
            'inflow': inflow,
            'outflow': outflow,
            'netflow': netflow
        }
        
        print("流量矩阵生成完成")
        print(f"流入矩阵形状: {inflow.shape}")
        print(f"流出矩阵形状: {outflow.shape}")
        print(f"净流量矩阵形状: {netflow.shape}")
        
    def visualize_data(self):
        """数据可视化"""
        print("\n正在生成可视化图表...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. 时间分布
        hourly_trips = self.data.groupby(self.data['otime_fmt'].dt.hour).size()
        axes[0, 0].plot(hourly_trips.index, hourly_trips.values)
        axes[0, 0].set_title('每小时出行次数分布')
        axes[0, 0].set_xlabel('小时')
        axes[0, 0].set_ylabel('出行次数')
        
        # 2. 空间分布热力图（上车点）
        pickup_heatmap = np.zeros((self.grid_size, self.grid_size))
        for _, row in self.data.iterrows():
            pickup_heatmap[int(row['o_grid_y']), int(row['o_grid_x'])] += 1
        
        im1 = axes[0, 1].imshow(pickup_heatmap, cmap='hot', interpolation='nearest')
        axes[0, 1].set_title('上车点空间分布热力图')
        plt.colorbar(im1, ax=axes[0, 1])
        
        # 3. 空间分布热力图（下车点）
        dropoff_heatmap = np.zeros((self.grid_size, self.grid_size))
        for _, row in self.data.iterrows():
            dropoff_heatmap[int(row['d_grid_y']), int(row['d_grid_x'])] += 1
        
        im2 = axes[0, 2].imshow(dropoff_heatmap, cmap='hot', interpolation='nearest')
        axes[0, 2].set_title('下车点空间分布热力图')
        plt.colorbar(im2, ax=axes[0, 2])
        
        # 4. 行程时长分布
        trip_duration_minutes = self.data['trip_duration'] / 60
        axes[1, 0].hist(trip_duration_minutes, bins=50, alpha=0.7)
        axes[1, 0].set_title('行程时长分布')
        axes[1, 0].set_xlabel('时长（分钟）')
        axes[1, 0].set_ylabel('频次')
        
        # 5. 流入流量时间序列
        total_inflow = np.sum(self.flow_data['inflow'], axis=(1, 2))
        axes[1, 1].plot(total_inflow)
        axes[1, 1].set_title('总流入量时间序列')
        axes[1, 1].set_xlabel('时间槽')
        axes[1, 1].set_ylabel('流入量')
        
        # 6. 流出流量时间序列
        total_outflow = np.sum(self.flow_data['outflow'], axis=(1, 2))
        axes[1, 2].plot(total_outflow)
        axes[1, 2].set_title('总流出量时间序列')
        axes[1, 2].set_xlabel('时间槽')
        axes[1, 2].set_ylabel('流出量')
        
        plt.tight_layout()
        plt.savefig('data_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def save_processed_data(self):
        """保存处理后的数据"""
        print("\n正在保存处理后的数据...")
        
        # 保存流量数据
        np.save('inflow_data.npy', self.flow_data['inflow'])
        np.save('outflow_data.npy', self.flow_data['outflow'])
        np.save('netflow_data.npy', self.flow_data['netflow'])
        
        # 保存元数据
        metadata = {
            'grid_size': self.grid_size,
            'time_interval': self.time_interval,
            'num_time_slots': self.num_time_slots,
            'lon_bins': self.lon_bins.tolist(),
            'lat_bins': self.lat_bins.tolist(),
            'time_slots': [str(t) for t in self.time_slots]
        }
        
        import json
        with open('metadata.json', 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print("数据保存完成")
        
    def process_all(self):
        """执行完整的数据处理流程"""
        self.load_data()
        self.explore_data()
        self.clean_data()
        self.create_spatial_grid()
        self.create_temporal_grid()
        self.generate_flow_matrices()
        self.visualize_data()
        self.save_processed_data()
        
        return self.flow_data

if __name__ == "__main__":
    # 创建数据处理器实例
    processor = TaxiDataProcessor('OD_ALL.csv', grid_size=32, time_interval=30)
    
    # 执行数据处理
    flow_data = processor.process_all()
    
    print("\n数据预处理完成！")
    print("生成的文件：")
    print("- inflow_data.npy: 流入量数据")
    print("- outflow_data.npy: 流出量数据") 
    print("- netflow_data.npy: 净流量数据")
    print("- metadata.json: 元数据信息")
    print("- data_analysis.png: 数据分析图表")
