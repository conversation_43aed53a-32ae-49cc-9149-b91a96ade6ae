import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import geopandas as gpd
from shapely.geometry import Point
import warnings
warnings.filterwarnings('ignore')

class TaxiDataProcessor:
    def __init__(self, csv_file, grid_shp_file=None, time_interval=30):
        """
        初始化出租车数据处理器

        Args:
            csv_file: OD数据文件路径
            grid_shp_file: 网格shp文件路径
            time_interval: 时间间隔（分钟）
        """
        self.csv_file = csv_file
        self.grid_shp_file = grid_shp_file or "SAU/SAU/Grid/BeijingGrid_1km_3857.shp"
        self.time_interval = time_interval
        self.data = None
        self.flow_data = None
        self.grid_gdf = None
        self.grid_size = None
        
    def load_data(self):
        """加载OD数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.csv_file)
        print(f"数据加载完成，共{len(self.data)}条记录")
        return self.data

    def load_grid_shapefile(self):
        """加载网格shp文件"""
        print(f"正在加载网格文件: {self.grid_shp_file}")
        try:
            self.grid_gdf = gpd.read_file(self.grid_shp_file)
            print(f"网格加载完成，共{len(self.grid_gdf)}个网格")
            print(f"网格坐标系: {self.grid_gdf.crs}")

            # 如果网格有ID列，使用它；否则创建索引
            if 'ID' in self.grid_gdf.columns:
                self.grid_gdf['grid_id'] = self.grid_gdf['ID']
            elif 'id' in self.grid_gdf.columns:
                self.grid_gdf['grid_id'] = self.grid_gdf['id']
            else:
                self.grid_gdf['grid_id'] = range(len(self.grid_gdf))

            # 计算网格的行列数（假设是规则网格）
            bounds = self.grid_gdf.total_bounds
            print(f"网格边界: {bounds}")

            # 估算网格大小（假设1km网格）
            self.grid_size = int(np.sqrt(len(self.grid_gdf)))
            print(f"估算网格大小: {self.grid_size}x{self.grid_size}")

            return self.grid_gdf

        except Exception as e:
            print(f"加载网格文件失败: {e}")
            print("将使用默认网格生成方法")
            return None
    
    def explore_data(self):
        """数据探索和分析"""
        print("\n=== 数据基本信息 ===")
        print(f"数据形状: {self.data.shape}")
        print(f"列名: {list(self.data.columns)}")
        print(f"数据类型:\n{self.data.dtypes}")
        
        print("\n=== 缺失值统计 ===")
        print(self.data.isnull().sum())
        
        print("\n=== 数据样本 ===")
        print(self.data.head())
        
        # 时间转换处理
        # 确保时间列为数值类型
        self.data['dtime'] = pd.to_numeric(self.data['dtime'], errors='coerce')
        self.data['otime'] = pd.to_numeric(self.data['otime'], errors='coerce')
        
        # 转换Unix时间戳为datetime对象
        self.data['dtime_utc'] = pd.to_datetime(self.data['dtime'], unit='s', utc=True)
        self.data['otime_utc'] = pd.to_datetime(self.data['otime'], unit='s', utc=True)
        
        # 转换为北京时间
        self.data['dtime_bj'] = self.data['dtime_utc'].dt.tz_convert('Asia/Shanghai')
        self.data['otime_bj'] = self.data['otime_utc'].dt.tz_convert('Asia/Shanghai')
        
        # 转换为无时区的本地时间
        self.data['dtime_fmt'] = self.data['dtime_bj'].dt.tz_localize(None)
        self.data['otime_fmt'] = self.data['otime_bj'].dt.tz_localize(None)
        
        # 时间范围分析
        print("\n=== 时间范围 ===")
        otime_min = self.data['otime_fmt'].min()
        otime_max = self.data['otime_fmt'].max()
        dtime_min = self.data['dtime_fmt'].min()
        dtime_max = self.data['dtime_fmt'].max()
        
        print(f"上车时间范围: {otime_min} 到 {otime_max}")
        print(f"下车时间范围: {dtime_min} 到 {dtime_max}")    
        print(self.data.head())
        start_time=self.data["otime_fmt"]
        end_time=self.data["dtime_fmt"]
        print(start_time)
        print(end_time)
        #保存时间文件
        save_path = 'time_range.csv'
        time_range_df = pd.DataFrame({
            'start_time': [start_time],
            'end_time': [end_time]
        })
        #保存所有数据
        self.data.to_csv('all_data.csv', index=False)
        time_range_df.to_csv(save_path, index=False)
        
        # #时间范围分析
        # print(f"\n=== 时间范围 ===")
        # print(f"上车时间范围: {self.data['otime_fmt'].min()} 到 {self.data['otime_fmt'].max()}")
        # print(f"下车时间范围: {self.data['dtime_fmt'].min()} 到 {self.data['dtime_fmt'].max()}")
        
        # 地理范围分析
        print(f"\n=== 地理范围 ===")
        print(f"上车经度范围: {self.data['olon'].min():.6f} 到 {self.data['olon'].max():.6f}")
        print(f"上车纬度范围: {self.data['olat'].min():.6f} 到 {self.data['olat'].max():.6f}")
        print(f"下车经度范围: {self.data['dlon'].min():.6f} 到 {self.data['dlon'].max():.6f}")
        print(f"下车纬度范围: {self.data['dlat'].min():.6f} 到 {self.data['dlat'].max():.6f}")
        
    def clean_data(self):
        """数据清洗"""
        print("\n正在进行数据清洗...")
        original_len = len(self.data)

        # 删除缺失值
        self.data = self.data.dropna()

        # 删除异常经纬度数据（北京地区范围）
        # 北京大致范围：经度115.7-117.4，纬度39.4-41.6
        self.data = self.data[
            (self.data['olon'] >= 115.7) & (self.data['olon'] <= 117.4) &
            (self.data['olat'] >= 39.4) & (self.data['olat'] <= 41.6) &
            (self.data['dlon'] >= 115.7) & (self.data['dlon'] <= 117.4) &
            (self.data['dlat'] >= 39.4) & (self.data['dlat'] <= 41.6)
        ]
        start_time=self.data["otime_fmt"].min()
        end_time=self.data["dtime_fmt"].max()
        
        

        self.data = self.data[
            (self.data['otime'] >= start_timestamp) & (self.data['otime'] <= end_timestamp) &
            (self.data['dtime'] >= start_timestamp) & (self.data['dtime'] <= end_timestamp)
        ]

        # 删除行程时间异常的数据（行程时间应为正数且合理）
        self.data['trip_duration'] = self.data['dtime'] - self.data['otime']
        self.data = self.data[
            (self.data['trip_duration'] > 0) &
            (self.data['trip_duration'] < 3600 * 6)  # 小于6小时
        ]

        print(f"数据清洗完成，从{original_len}条记录减少到{len(self.data)}条记录")
        
    def create_spatial_grid(self):
        """创建空间网格"""
        print("\n正在进行空间网格化...")

        # 首先尝试加载shp文件
        if self.load_grid_shapefile() is not None:
            self._assign_points_to_grid_shapefile()
        else:
            self._create_regular_grid()

        print("空间网格创建完成")

    def _assign_points_to_grid_shapefile(self):
        """使用shp文件进行点位网格分配"""
        print("使用shp文件进行空间网格分配...")

        # 确保坐标系一致（转换为WGS84）
        if self.grid_gdf.crs != 'EPSG:4326':
            grid_wgs84 = self.grid_gdf.to_crs('EPSG:4326')
        else:
            grid_wgs84 = self.grid_gdf.copy()

        # 创建上车点和下车点的几何对象
        pickup_points = [Point(lon, lat) for lon, lat in zip(self.data['olon'], self.data['olat'])]
        dropoff_points = [Point(lon, lat) for lon, lat in zip(self.data['dlon'], self.data['dlat'])]

        pickup_gdf = gpd.GeoDataFrame(geometry=pickup_points, crs='EPSG:4326')
        dropoff_gdf = gpd.GeoDataFrame(geometry=dropoff_points, crs='EPSG:4326')

        # 空间连接：找到每个点所在的网格
        pickup_joined = gpd.sjoin(pickup_gdf, grid_wgs84, how='left', predicate='within')
        dropoff_joined = gpd.sjoin(dropoff_gdf, grid_wgs84, how='left', predicate='within')

        # 分配网格ID
        self.data['o_grid_id'] = pickup_joined['grid_id'].values
        self.data['d_grid_id'] = dropoff_joined['grid_id'].values

        # 处理未匹配的点（使用最近邻）
        pickup_na_mask = self.data['o_grid_id'].isna()
        dropoff_na_mask = self.data['d_grid_id'].isna()

        if pickup_na_mask.sum() > 0:
            print(f"有{pickup_na_mask.sum()}个上车点未匹配到网格，使用最近邻方法")
            self._assign_nearest_grid(pickup_points, pickup_na_mask, 'o_grid_id', grid_wgs84)

        if dropoff_na_mask.sum() > 0:
            print(f"有{dropoff_na_mask.sum()}个下车点未匹配到网格，使用最近邻方法")
            self._assign_nearest_grid(dropoff_points, dropoff_na_mask, 'd_grid_id', grid_wgs84)

        # 创建网格ID到行列索引的映射
        self._create_grid_mapping()

    def _assign_nearest_grid(self, points, mask, column_name, grid_gdf):
        """为未匹配的点分配最近的网格"""
        try:
            from scipy.spatial import cKDTree
        except ImportError:
            print("警告: scipy未安装，跳过最近邻分配")
            return

        # 获取网格中心点
        grid_centroids = grid_gdf.geometry.centroid
        grid_coords = np.array([[p.x, p.y] for p in grid_centroids])

        # 获取未匹配的点坐标
        unmatched_indices = np.where(mask)[0]
        unmatched_coords = np.array([[points[i].x, points[i].y] for i in unmatched_indices])

        # 构建KDTree并查找最近邻
        tree = cKDTree(grid_coords)
        _, nearest_indices = tree.query(unmatched_coords)

        # 分配网格ID
        for i, nearest_idx in enumerate(nearest_indices):
            original_idx = unmatched_indices[i]
            self.data.loc[original_idx, column_name] = grid_gdf.iloc[nearest_idx]['grid_id']

    def _create_grid_mapping(self):
        """创建网格ID到行列索引的映射"""
        unique_grid_ids = sorted(self.data[['o_grid_id', 'd_grid_id']].stack().dropna().unique())

        # 创建网格ID到索引的映射
        self.grid_id_to_index = {grid_id: idx for idx, grid_id in enumerate(unique_grid_ids)}
        self.index_to_grid_id = {idx: grid_id for grid_id, idx in self.grid_id_to_index.items()}

        # 更新网格大小
        self.grid_size = len(unique_grid_ids)

        # 为了兼容原有代码，创建虚拟的行列索引
        grid_sqrt = int(np.sqrt(self.grid_size))
        if grid_sqrt * grid_sqrt < self.grid_size:
            grid_sqrt += 1

        self.data['o_grid_x'] = self.data['o_grid_id'].map(lambda x: self.grid_id_to_index.get(x, 0) % grid_sqrt)
        self.data['o_grid_y'] = self.data['o_grid_id'].map(lambda x: self.grid_id_to_index.get(x, 0) // grid_sqrt)
        self.data['d_grid_x'] = self.data['d_grid_id'].map(lambda x: self.grid_id_to_index.get(x, 0) % grid_sqrt)
        self.data['d_grid_y'] = self.data['d_grid_id'].map(lambda x: self.grid_id_to_index.get(x, 0) // grid_sqrt)

        print(f"网格映射创建完成，共{self.grid_size}个有效网格，映射为{grid_sqrt}x{grid_sqrt}矩阵")

    def _create_regular_grid(self):
        """创建规则网格（备用方法）"""
        print("使用规则网格生成方法...")

        if self.grid_size is None:
            self.grid_size = 32

        # 计算经纬度范围
        min_lon = self.data[['olon', 'dlon']].min().min()
        max_lon = self.data[['olon', 'dlon']].max().max()
        min_lat = self.data[['olat', 'dlat']].min().min()
        max_lat = self.data[['olat', 'dlat']].max().max()

        # 创建网格边界
        lon_bins = np.linspace(min_lon, max_lon, self.grid_size + 1)
        lat_bins = np.linspace(min_lat, max_lat, self.grid_size + 1)

        # 为上车和下车位置分配网格索引
        self.data['o_grid_x'] = np.digitize(self.data['olon'], lon_bins) - 1
        self.data['o_grid_y'] = np.digitize(self.data['olat'], lat_bins) - 1
        self.data['d_grid_x'] = np.digitize(self.data['dlon'], lon_bins) - 1
        self.data['d_grid_y'] = np.digitize(self.data['dlat'], lat_bins) - 1

        # 确保网格索引在有效范围内
        self.data['o_grid_x'] = np.clip(self.data['o_grid_x'], 0, self.grid_size - 1)
        self.data['o_grid_y'] = np.clip(self.data['o_grid_y'], 0, self.grid_size - 1)
        self.data['d_grid_x'] = np.clip(self.data['d_grid_x'], 0, self.grid_size - 1)
        self.data['d_grid_y'] = np.clip(self.data['d_grid_y'], 0, self.grid_size - 1)

        self.lon_bins = lon_bins
        self.lat_bins = lat_bins
        
    def create_temporal_grid(self):
        """创建时间网格"""
        print(f"\n正在创建时间网格（间隔{self.time_interval}分钟）...")

        # 转换时间戳为datetime
        self.data['otime_dt'] = pd.to_datetime(self.data['otime'], unit='s')
        self.data['dtime_dt'] = pd.to_datetime(self.data['dtime'], unit='s')

        # 获取时间范围（限制在2016年8月）
        start_time = pd.Timestamp('2016-08-01 00:00:00')
        end_time = pd.Timestamp('2016-08-31 23:59:59')

        # 创建时间间隔
        time_slots = pd.date_range(start=start_time, end=end_time,
                                  freq=f'{self.time_interval}min')

        # 为每条记录分配时间槽
        self.data['o_time_slot'] = pd.cut(self.data['otime_dt'], bins=time_slots,
                                         labels=False, include_lowest=True)
        self.data['d_time_slot'] = pd.cut(self.data['dtime_dt'], bins=time_slots,
                                         labels=False, include_lowest=True)

        self.time_slots = time_slots
        self.num_time_slots = len(time_slots) - 1

        print(f"时间网格创建完成，共{self.num_time_slots}个时间槽")
        
    def generate_flow_matrices(self):
        """生成流量矩阵"""
        print("\n正在生成流量矩阵...")

        # 使用更合理的网格大小（减少内存使用）
        max_grid_size = 64  # 限制最大网格大小
        if self.grid_size > max_grid_size:
            print(f"网格大小{self.grid_size}过大，调整为{max_grid_size}x{max_grid_size}")
            self.grid_size = max_grid_size

        # 初始化流入和流出矩阵
        print(f"初始化矩阵: ({self.num_time_slots}, {self.grid_size}, {self.grid_size})")
        inflow = np.zeros((self.num_time_slots, self.grid_size, self.grid_size), dtype=np.float32)
        outflow = np.zeros((self.num_time_slots, self.grid_size, self.grid_size), dtype=np.float32)

        # 重新映射网格索引到新的大小
        if hasattr(self, 'grid_id_to_index'):
            # 使用网格ID映射
            valid_data = self.data.dropna(subset=['o_time_slot', 'd_time_slot', 'o_grid_id', 'd_grid_id'])

            for _, row in valid_data.iterrows():
                # 流出（上车）
                t_slot = int(row['o_time_slot'])
                if 0 <= t_slot < self.num_time_slots:
                    grid_idx = self.grid_id_to_index.get(row['o_grid_id'], 0)
                    grid_x = grid_idx % self.grid_size
                    grid_y = (grid_idx // self.grid_size) % self.grid_size
                    outflow[t_slot, grid_y, grid_x] += 1

                # 流入（下车）
                t_slot = int(row['d_time_slot'])
                if 0 <= t_slot < self.num_time_slots:
                    grid_idx = self.grid_id_to_index.get(row['d_grid_id'], 0)
                    grid_x = grid_idx % self.grid_size
                    grid_y = (grid_idx // self.grid_size) % self.grid_size
                    inflow[t_slot, grid_y, grid_x] += 1
        else:
            # 使用原有的行列索引
            valid_data = self.data.dropna(subset=['o_time_slot', 'd_time_slot'])

            for _, row in valid_data.iterrows():
                # 流出（上车）
                t_slot = int(row['o_time_slot'])
                if 0 <= t_slot < self.num_time_slots:
                    grid_x = min(int(row['o_grid_x']), self.grid_size - 1)
                    grid_y = min(int(row['o_grid_y']), self.grid_size - 1)
                    outflow[t_slot, grid_y, grid_x] += 1

                # 流入（下车）
                t_slot = int(row['d_time_slot'])
                if 0 <= t_slot < self.num_time_slots:
                    grid_x = min(int(row['d_grid_x']), self.grid_size - 1)
                    grid_y = min(int(row['d_grid_y']), self.grid_size - 1)
                    inflow[t_slot, grid_y, grid_x] += 1

        # 计算净流量
        netflow = inflow - outflow

        self.flow_data = {
            'inflow': inflow,
            'outflow': outflow,
            'netflow': netflow
        }

        print("流量矩阵生成完成")
        print(f"流入矩阵形状: {inflow.shape}")
        print(f"流出矩阵形状: {outflow.shape}")
        print(f"净流量矩阵形状: {netflow.shape}")
        print(f"总流入量: {inflow.sum():.0f}")
        print(f"总流出量: {outflow.sum():.0f}")
        
    def visualize_data(self):
        """数据可视化"""
        print("\n正在生成可视化图表...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 1. 时间分布
        hourly_trips = self.data.groupby(self.data['otime_dt'].dt.hour).size()
        axes[0, 0].plot(hourly_trips.index, hourly_trips.values)
        axes[0, 0].set_title('每小时出行次数分布')
        axes[0, 0].set_xlabel('小时')
        axes[0, 0].set_ylabel('出行次数')
        
        # 2. 空间分布热力图（上车点）
        pickup_heatmap = np.zeros((self.grid_size, self.grid_size))
        for _, row in self.data.iterrows():
            pickup_heatmap[int(row['o_grid_y']), int(row['o_grid_x'])] += 1
        
        im1 = axes[0, 1].imshow(pickup_heatmap, cmap='hot', interpolation='nearest')
        axes[0, 1].set_title('上车点空间分布热力图')
        plt.colorbar(im1, ax=axes[0, 1])
        
        # 3. 空间分布热力图（下车点）
        dropoff_heatmap = np.zeros((self.grid_size, self.grid_size))
        for _, row in self.data.iterrows():
            dropoff_heatmap[int(row['d_grid_y']), int(row['d_grid_x'])] += 1
        
        im2 = axes[0, 2].imshow(dropoff_heatmap, cmap='hot', interpolation='nearest')
        axes[0, 2].set_title('下车点空间分布热力图')
        plt.colorbar(im2, ax=axes[0, 2])
        
        # 4. 行程时长分布
        trip_duration_minutes = self.data['trip_duration'] / 60
        axes[1, 0].hist(trip_duration_minutes, bins=50, alpha=0.7)
        axes[1, 0].set_title('行程时长分布')
        axes[1, 0].set_xlabel('时长（分钟）')
        axes[1, 0].set_ylabel('频次')
        
        # 5. 流入流量时间序列
        total_inflow = np.sum(self.flow_data['inflow'], axis=(1, 2))
        axes[1, 1].plot(total_inflow)
        axes[1, 1].set_title('总流入量时间序列')
        axes[1, 1].set_xlabel('时间槽')
        axes[1, 1].set_ylabel('流入量')
        
        # 6. 流出流量时间序列
        total_outflow = np.sum(self.flow_data['outflow'], axis=(1, 2))
        axes[1, 2].plot(total_outflow)
        axes[1, 2].set_title('总流出量时间序列')
        axes[1, 2].set_xlabel('时间槽')
        axes[1, 2].set_ylabel('流出量')
        
        plt.tight_layout()
        plt.savefig('data_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def save_processed_data(self):
        """保存处理后的数据"""
        print("\n正在保存处理后的数据...")
        
        # 保存流量数据
        np.save('inflow_data.npy', self.flow_data['inflow'])
        np.save('outflow_data.npy', self.flow_data['outflow'])
        np.save('netflow_data.npy', self.flow_data['netflow'])
        
        # 保存元数据
        metadata = {
            'grid_size': self.grid_size,
            'time_interval': self.time_interval,
            'num_time_slots': self.num_time_slots,
            'lon_bins': self.lon_bins.tolist(),
            'lat_bins': self.lat_bins.tolist(),
            'time_slots': [str(t) for t in self.time_slots]
        }
        
        import json
        with open('metadata.json', 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print("数据保存完成")
        
    def process_all(self):
        """执行完整的数据处理流程"""
        self.load_data()
        self.explore_data()
        self.clean_data()
        self.create_spatial_grid()
        self.create_temporal_grid()
        self.generate_flow_matrices()
        self.visualize_data()
        self.save_processed_data()
        
        return self.flow_data

if __name__ == "__main__":
    # 创建数据处理器实例，使用shp文件
    processor = TaxiDataProcessor('OD_ALL.csv',
                                 grid_shp_file="SAU/SAU/Grid/BeijingGrid_1km_3857.shp",
                                 time_interval=30)

    # 执行数据处理
    flow_data = processor.process_all()
    
    print("\n数据预处理完成！")
    print("生成的文件：")
    print("- inflow_data.npy: 流入量数据")
    print("- outflow_data.npy: 流出量数据") 
    print("- netflow_data.npy: 净流量数据")
    print("- metadata.json: 元数据信息")
    print("- data_analysis.png: 数据分析图表")
