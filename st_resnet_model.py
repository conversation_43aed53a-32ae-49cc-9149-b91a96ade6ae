import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ResidualUnit(nn.Module):
    """残差单元"""
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1):
        super(ResidualUnit, self).__init__()
        
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size, 
                              stride=stride, padding=kernel_size//2, bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size, 
                              stride=1, padding=kernel_size//2, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)
        
        # 如果输入输出通道数不同，需要调整维度
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, 
                         stride=stride, bias=False),
                nn.BatchNorm2d(out_channels)
            )
    
    def forward(self, x):
        residual = self.shortcut(x)
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        
        out += residual
        out = self.relu(out)
        
        return out

class STResNet(nn.Module):
    """ST-ResNet时空残差网络"""
    def __init__(self, c_conf=(3, 2, 32, 32), p_conf=(3, 2, 32, 32), 
                 t_conf=(3, 2, 32, 32), external_dim=8, nb_residual_unit=4):
        """
        初始化ST-ResNet模型
        
        Args:
            c_conf: 近期依赖配置 (len_closeness, nb_flow, map_height, map_width)
            p_conf: 周期依赖配置 (len_period, nb_flow, map_height, map_width)  
            t_conf: 趋势依赖配置 (len_trend, nb_flow, map_height, map_width)
            external_dim: 外部特征维度
            nb_residual_unit: 残差单元数量
        """
        super(STResNet, self).__init__()
        
        self.c_conf = c_conf
        self.p_conf = p_conf
        self.t_conf = t_conf
        self.external_dim = external_dim
        self.nb_residual_unit = nb_residual_unit
        
        # 近期依赖分支
        if c_conf is not None:
            self.c_way = self._make_one_way(c_conf)
        
        # 周期依赖分支  
        if p_conf is not None:
            self.p_way = self._make_one_way(p_conf)
            
        # 趋势依赖分支
        if t_conf is not None:
            self.t_way = self._make_one_way(t_conf)
        
        # 融合层
        nb_flow = c_conf[1] if c_conf is not None else p_conf[1] if p_conf is not None else t_conf[1]
        
        # 计算融合输入维度
        fusion_input_dim = 0
        if c_conf is not None:
            fusion_input_dim += nb_flow
        if p_conf is not None:
            fusion_input_dim += nb_flow
        if t_conf is not None:
            fusion_input_dim += nb_flow
        if external_dim > 0:
            fusion_input_dim += external_dim
            
        self.fusion = nn.Sequential(
            nn.Conv2d(fusion_input_dim, nb_flow, kernel_size=1, padding=0),
            nn.ReLU(inplace=True),
            nn.Conv2d(nb_flow, nb_flow, kernel_size=1, padding=0)
        )
        
        # 外部特征处理
        if external_dim > 0:
            map_height = c_conf[2] if c_conf is not None else p_conf[2] if p_conf is not None else t_conf[2]
            map_width = c_conf[3] if c_conf is not None else p_conf[3] if p_conf is not None else t_conf[3]
            
            self.external_ops = nn.Sequential(
                nn.Linear(external_dim, 10),
                nn.ReLU(inplace=True),
                nn.Linear(10, nb_flow * map_height * map_width),
                nn.ReLU(inplace=True)
            )
    
    def _make_one_way(self, conf):
        """构建一个分支网络"""
        len_seq, nb_flow, map_height, map_width = conf
        
        layers = []
        
        # 第一层卷积
        layers.append(nn.Conv2d(len_seq * nb_flow, 64, kernel_size=3, padding=1))
        layers.append(nn.ReLU(inplace=True))
        
        # 残差单元
        in_channels = 64
        for i in range(self.nb_residual_unit):
            layers.append(ResidualUnit(in_channels, 64))
            in_channels = 64
        
        # 输出层
        layers.append(nn.Conv2d(64, nb_flow, kernel_size=3, padding=1))
        layers.append(nn.ReLU(inplace=True))
        
        return nn.Sequential(*layers)
    
    def forward(self, inputs):
        """前向传播"""
        c, p, t, external = inputs
        
        # 各分支输出
        outputs = []
        
        # 近期依赖
        if self.c_conf is not None and c is not None:
            # 重塑输入: (batch, len_closeness, nb_flow, H, W) -> (batch, len_closeness*nb_flow, H, W)
            batch_size = c.size(0)
            c_reshaped = c.view(batch_size, -1, c.size(3), c.size(4))
            c_out = self.c_way(c_reshaped)
            outputs.append(c_out)
        
        # 周期依赖
        if self.p_conf is not None and p is not None:
            batch_size = p.size(0)
            p_reshaped = p.view(batch_size, -1, p.size(3), p.size(4))
            p_out = self.p_way(p_reshaped)
            outputs.append(p_out)
        
        # 趋势依赖
        if self.t_conf is not None and t is not None:
            batch_size = t.size(0)
            t_reshaped = t.view(batch_size, -1, t.size(3), t.size(4))
            t_out = self.t_way(t_reshaped)
            outputs.append(t_out)
        
        # 外部特征
        if self.external_dim > 0 and external is not None:
            # 处理外部特征
            external_out = self.external_ops(external)
            
            # 重塑为空间维度
            batch_size = external_out.size(0)
            nb_flow = self.c_conf[1] if self.c_conf is not None else self.p_conf[1] if self.p_conf is not None else self.t_conf[1]
            map_height = self.c_conf[2] if self.c_conf is not None else self.p_conf[2] if self.p_conf is not None else self.t_conf[2]
            map_width = self.c_conf[3] if self.c_conf is not None else self.p_conf[3] if self.p_conf is not None else self.t_conf[3]
            
            external_out = external_out.view(batch_size, nb_flow, map_height, map_width)
            outputs.append(external_out)
        
        # 融合所有分支
        if len(outputs) == 1:
            main_output = outputs[0]
        else:
            main_output = torch.cat(outputs, dim=1)
            main_output = self.fusion(main_output)
        
        return main_output

class STResNetTrainer:
    """ST-ResNet训练器"""
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        
    def train_epoch(self, train_loader, optimizer, criterion):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        for batch_idx, (inputs, targets) in enumerate(train_loader):
            # 移动数据到设备
            c, p, t, external = inputs
            if c is not None:
                c = c.to(self.device)
            if p is not None:
                p = p.to(self.device)
            if t is not None:
                t = t.to(self.device)
            if external is not None:
                external = external.to(self.device)
            
            targets = targets.to(self.device)
            
            # 前向传播
            optimizer.zero_grad()
            outputs = self.model([c, p, t, external])
            loss = criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 100 == 0:
                print(f'Batch {batch_idx}, Loss: {loss.item():.6f}')
        
        return total_loss / num_batches
    
    def validate(self, val_loader, criterion):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        num_batches = 0
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                c, p, t, external = inputs
                if c is not None:
                    c = c.to(self.device)
                if p is not None:
                    p = p.to(self.device)
                if t is not None:
                    t = t.to(self.device)
                if external is not None:
                    external = external.to(self.device)
                
                targets = targets.to(self.device)
                
                outputs = self.model([c, p, t, external])
                loss = criterion(outputs, targets)
                
                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def predict(self, test_loader):
        """预测"""
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for inputs, _ in test_loader:
                c, p, t, external = inputs
                if c is not None:
                    c = c.to(self.device)
                if p is not None:
                    p = p.to(self.device)
                if t is not None:
                    t = t.to(self.device)
                if external is not None:
                    external = external.to(self.device)
                
                outputs = self.model([c, p, t, external])
                predictions.append(outputs.cpu().numpy())
        
        return np.concatenate(predictions, axis=0)

def create_st_resnet_model(grid_size=32, nb_flow=2, len_closeness=3, 
                          len_period=1, len_trend=1, external_dim=8):
    """创建ST-ResNet模型"""
    
    c_conf = (len_closeness, nb_flow, grid_size, grid_size) if len_closeness > 0 else None
    p_conf = (len_period, nb_flow, grid_size, grid_size) if len_period > 0 else None
    t_conf = (len_trend, nb_flow, grid_size, grid_size) if len_trend > 0 else None
    
    model = STResNet(
        c_conf=c_conf,
        p_conf=p_conf, 
        t_conf=t_conf,
        external_dim=external_dim,
        nb_residual_unit=4
    )
    
    return model

if __name__ == "__main__":
    # 测试模型
    model = create_st_resnet_model()
    print("ST-ResNet模型创建成功！")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")
    
    # 测试前向传播
    batch_size = 4
    c = torch.randn(batch_size, 3, 2, 32, 32)  # 近期依赖
    p = torch.randn(batch_size, 1, 2, 32, 32)  # 周期依赖
    t = torch.randn(batch_size, 1, 2, 32, 32)  # 趋势依赖
    external = torch.randn(batch_size, 8)       # 外部特征
    
    output = model([c, p, t, external])
    print(f"输出形状: {output.shape}")
