import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import Step<PERSON>, ReduceLROnPlateau
import numpy as np
import matplotlib.pyplot as plt
import time
import os
from datetime import datetime
import json

from st_resnet_model import create_st_resnet_model, STResNetTrainer
from data_loader import STDataProcessor

class TrainingConfig:
    """训练配置类"""
    def __init__(self):
        # 模型参数
        self.grid_size = 32
        self.nb_flow = 2  # 流入和流出
        self.len_closeness = 3
        self.len_period = 1
        self.len_trend = 1
        self.external_dim = 8
        
        # 训练参数
        self.batch_size = 32
        self.num_epochs = 100
        self.learning_rate = 0.001
        self.weight_decay = 1e-4
        
        # 学习率调度
        self.lr_scheduler = 'step'  # 'step' or 'plateau'
        self.step_size = 20
        self.gamma = 0.5
        self.patience = 10
        
        # 早停参数
        self.early_stopping = True
        self.early_stopping_patience = 15
        
        # 设备
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 保存路径
        self.model_save_path = 'checkpoints'
        self.log_save_path = 'logs'
        
        # 创建目录
        os.makedirs(self.model_save_path, exist_ok=True)
        os.makedirs(self.log_save_path, exist_ok=True)

class EarlyStopping:
    """早停机制"""
    def __init__(self, patience=15, min_delta=0, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_loss = None
        self.counter = 0
        self.best_weights = None
        
    def __call__(self, val_loss, model):
        if self.best_loss is None:
            self.best_loss = val_loss
            self.save_checkpoint(model)
        elif val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            self.save_checkpoint(model)
        else:
            self.counter += 1
            
        if self.counter >= self.patience:
            if self.restore_best_weights:
                model.load_state_dict(self.best_weights)
            return True
        return False
    
    def save_checkpoint(self, model):
        self.best_weights = model.state_dict().copy()

class TrainingLogger:
    """训练日志记录器"""
    def __init__(self, log_dir='logs'):
        self.log_dir = log_dir
        self.train_losses = []
        self.val_losses = []
        self.learning_rates = []
        
        # 创建日志文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.log_file = os.path.join(log_dir, f'training_log_{timestamp}.json')
        
    def log_epoch(self, epoch, train_loss, val_loss, lr, epoch_time):
        """记录一个epoch的信息"""
        self.train_losses.append(train_loss)
        self.val_losses.append(val_loss)
        self.learning_rates.append(lr)
        
        log_entry = {
            'epoch': epoch,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'learning_rate': lr,
            'epoch_time': epoch_time,
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存到文件
        with open(self.log_file, 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
        
        print(f'Epoch {epoch:3d} | Train Loss: {train_loss:.6f} | '
              f'Val Loss: {val_loss:.6f} | LR: {lr:.6f} | Time: {epoch_time:.2f}s')
    
    def plot_training_curves(self, save_path='training_curves.png'):
        """绘制训练曲线"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 损失曲线
        epochs = range(1, len(self.train_losses) + 1)
        ax1.plot(epochs, self.train_losses, 'b-', label='Training Loss')
        ax1.plot(epochs, self.val_losses, 'r-', label='Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.legend()
        ax1.grid(True)
        
        # 学习率曲线
        ax2.plot(epochs, self.learning_rates, 'g-', label='Learning Rate')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Learning Rate')
        ax2.set_title('Learning Rate Schedule')
        ax2.legend()
        ax2.grid(True)
        ax2.set_yscale('log')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

def train_st_resnet():
    """训练ST-ResNet模型"""
    print("开始训练ST-ResNet模型...")
    
    # 加载配置
    config = TrainingConfig()
    print(f"使用设备: {config.device}")
    
    # 数据处理
    print("\n=== 数据处理 ===")
    data_processor = STDataProcessor()
    train_loader, val_loader, test_loader, scaler = data_processor.process_all(
        len_closeness=config.len_closeness,
        len_period=config.len_period,
        len_trend=config.len_trend,
        batch_size=config.batch_size
    )
    
    # 创建模型
    print("\n=== 创建模型 ===")
    model = create_st_resnet_model(
        grid_size=config.grid_size,
        nb_flow=config.nb_flow,
        len_closeness=config.len_closeness,
        len_period=config.len_period,
        len_trend=config.len_trend,
        external_dim=config.external_dim
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建训练器
    trainer = STResNetTrainer(model, config.device)
    
    # 损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=config.learning_rate, 
                          weight_decay=config.weight_decay)
    
    # 学习率调度器
    if config.lr_scheduler == 'step':
        scheduler = StepLR(optimizer, step_size=config.step_size, gamma=config.gamma)
    elif config.lr_scheduler == 'plateau':
        scheduler = ReduceLROnPlateau(optimizer, mode='min', factor=config.gamma,
                                    patience=config.patience, verbose=True)
    
    # 早停和日志
    early_stopping = EarlyStopping(patience=config.early_stopping_patience) if config.early_stopping else None
    logger = TrainingLogger(config.log_save_path)
    
    # 训练循环
    print("\n=== 开始训练 ===")
    best_val_loss = float('inf')
    
    for epoch in range(1, config.num_epochs + 1):
        start_time = time.time()
        
        # 训练
        train_loss = trainer.train_epoch(train_loader, optimizer, criterion)
        
        # 验证
        val_loss = trainer.validate(val_loader, criterion)
        
        # 学习率调度
        if config.lr_scheduler == 'step':
            scheduler.step()
        elif config.lr_scheduler == 'plateau':
            scheduler.step(val_loss)
        
        # 记录日志
        current_lr = optimizer.param_groups[0]['lr']
        epoch_time = time.time() - start_time
        logger.log_epoch(epoch, train_loss, val_loss, current_lr, epoch_time)
        
        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': val_loss,
                'config': config.__dict__,
                'scaler': scaler
            }, os.path.join(config.model_save_path, 'best_model.pth'))
        
        # 早停检查
        if early_stopping and early_stopping(val_loss, model):
            print(f"早停触发，在第{epoch}轮停止训练")
            break
    
    # 绘制训练曲线
    logger.plot_training_curves(os.path.join(config.log_save_path, 'training_curves.png'))
    
    # 保存最终模型
    torch.save({
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'val_loss': val_loss,
        'config': config.__dict__,
        'scaler': scaler
    }, os.path.join(config.model_save_path, 'final_model.pth'))
    
    print(f"\n训练完成！最佳验证损失: {best_val_loss:.6f}")
    print(f"模型保存在: {config.model_save_path}")
    print(f"日志保存在: {config.log_save_path}")
    
    return model, trainer, test_loader, scaler

if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 训练模型
    model, trainer, test_loader, scaler = train_st_resnet()
    
    print("\n训练脚本执行完成！")
