#!/usr/bin/env python3
"""
安装项目依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} 安装失败")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("城市出租车出行流量预测项目 - 依赖包安装")
    print("=" * 60)
    
    # 必需的包列表
    required_packages = [
        "torch",
        "torchvision", 
        "numpy",
        "pandas",
        "matplotlib",
        "seaborn",
        "scikit-learn",
        "geopandas",
        "shapely",
        "scipy"
    ]
    
    print("正在安装以下依赖包:")
    for package in required_packages:
        print(f"  - {package}")
    print()
    
    # 升级pip
    print("升级pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✓ pip升级成功")
    except subprocess.CalledProcessError:
        print("✗ pip升级失败，继续安装其他包")
    
    print()
    
    # 安装包
    success_count = 0
    failed_packages = []
    
    for package in required_packages:
        print(f"正在安装 {package}...")
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    print()
    print("=" * 60)
    print("安装结果:")
    print(f"成功安装: {success_count}/{len(required_packages)} 个包")
    
    if failed_packages:
        print("安装失败的包:")
        for package in failed_packages:
            print(f"  - {package}")
        print()
        print("请手动安装失败的包:")
        print(f"pip install {' '.join(failed_packages)}")
    else:
        print("所有依赖包安装成功！")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
